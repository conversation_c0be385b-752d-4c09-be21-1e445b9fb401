version: '3'

# OnlyRules Project Taskfile
# Centralized command runner for development, build, test, and deployment tasks
# Usage: task <command> or task --list to see all available tasks

vars:
  WEB_DIR: packages/web
  NODE_ENV: '{{.NODE_ENV | default "development"}}'

# Global environment variables
env:
  NODE_ENV: '{{.NODE_ENV}}'

tasks:
  # =============================================================================
  # HELP & INFO TASKS
  # =============================================================================
  
  default:
    desc: "Show available tasks"
    cmds:
      - task --list
    silent: true

  info:
    desc: "Show project information and environment"
    cmds:
      - echo "OnlyRules - AI Prompt Management Platform"
      - sh -c 'echo "Node.js version:" && node --version'
      - sh -c 'echo "npm version:" && npm --version'
      - echo "Environment{{":"}} {{.NODE_ENV}}"
      - echo "Web package location{{":"}} {{.WEB_DIR}}"
    silent: true

  # =============================================================================
  # DEVELOPMENT TASKS
  # =============================================================================

  dev:
    desc: "Start the development server"
    cmds:
      - npm run dev
    
  dev:watch:
    desc: "Start development server with file watching (alias for dev)"
    cmds:
      - task: dev

  start:
    desc: "Start the production server"
    deps: [build]
    cmds:
      - npm run start

  # =============================================================================
  # BUILD TASKS
  # =============================================================================

  build:
    desc: "Build the application for production"
    deps: [type-check, db:generate]
    cmds:
      - npm run build

  build:clean:
    desc: "Clean build artifacts and rebuild"
    cmds:
      - task: clean
      - task: build

  type-check:
    desc: "Run TypeScript type checking"
    cmds:
      - npm run type-check

  lint:
    desc: "Run ESLint to check code quality"
    cmds:
      - npm run lint

  lint:fix:
    desc: "Run ESLint and automatically fix issues"
    cmds:
      - npm --prefix {{.WEB_DIR}} run lint -- --fix

  # =============================================================================
  # DATABASE TASKS
  # =============================================================================

  db:generate:
    desc: "Generate Prisma client"
    cmds:
      - npm run db:generate

  db:push:
    desc: "Push database schema changes"
    cmds:
      - npm run db:push

  db:migrate:
    desc: "Run database migrations (development)"
    cmds:
      - npm run db:migrate

  db:migrate:deploy:
    desc: "Deploy database migrations (production)"
    cmds:
      - npm run db:migrate:deploy

  db:migrate:reset:
    desc: "Reset database and run all migrations"
    cmds:
      - npm run db:migrate:reset

  db:seed:
    desc: "Seed the database with initial data"
    deps: [db:generate]
    cmds:
      - npm run db:seed

  db:studio:
    desc: "Open Prisma Studio for database management"
    cmds:
      - npm run db:studio

  db:setup:
    desc: "Complete database setup (generate, migrate, seed)"
    cmds:
      - task: db:generate
      - task: db:migrate
      - task: db:seed

  # =============================================================================
  # TESTING TASKS
  # =============================================================================

  test:
    desc: "Run tests in watch mode"
    cmds:
      - npm run test

  test:run:
    desc: "Run all tests once"
    cmds:
      - npm run test:run

  test:coverage:
    desc: "Run tests with coverage report"
    cmds:
      - npm run test:coverage

  test:ui:
    desc: "Run tests with UI interface"
    cmds:
      - npm run test:ui

  test:ci:
    desc: "Run tests for CI/CD (no watch mode)"
    cmds:
      - task: test:run

  # =============================================================================
  # INTERNATIONALIZATION TASKS
  # =============================================================================

  i18n:extract:
    desc: "Extract translatable strings"
    cmds:
      - npm run lingui:extract

  i18n:compile:
    desc: "Compile translation files"
    cmds:
      - npm run lingui:compile

  i18n:update:
    desc: "Extract and compile translations"
    cmds:
      - task: i18n:extract
      - task: i18n:compile

  # =============================================================================
  # DEPLOYMENT TASKS
  # =============================================================================

  deploy:verify:
    desc: "Verify build is ready for deployment"
    cmds:
      - task: type-check
      - task: lint
      - task: test:run
      - task: build
      - echo "✅ Build verification complete - ready for deployment"

  deploy:build:
    desc: "Build for deployment with all checks"
    cmds:
      - task: install:clean
      - task: deploy:verify

  # =============================================================================
  # UTILITY TASKS
  # =============================================================================

  clean:
    desc: "Clean build artifacts and cache files"
    cmds:
      - rm -rf {{.WEB_DIR}}/.next
      - rm -rf {{.WEB_DIR}}/dist
      - rm -rf {{.WEB_DIR}}/coverage
      - rm -rf .task
      - echo "✅ Cleaned build artifacts"

  clean:next:
    desc: "Remove Next.js build cache (.next directory)"
    cmds:
      - rm -rf {{.WEB_DIR}}/.next
      - echo "✅ Removed .next directory"

  install:
    desc: "Install dependencies"
    cmds:
      - npm install

  install:clean:
    desc: "Clean install all dependencies"
    cmds:
      - npm run install:clean

  format:
    desc: "Format code using Prettier (via ESLint)"
    cmds:
      - task: lint:fix

  verify:migration:
    desc: "Verify Radix UI migration status"
    cmds:
      - bash {{.WEB_DIR}}/scripts/verify-migration.sh

  # =============================================================================
  # COMPOSITE TASKS
  # =============================================================================

  setup:
    desc: "Complete project setup for new developers"
    cmds:
      - task: install
      - task: db:setup
      - task: i18n:compile
      - echo "✅ Project setup complete! Run 'task dev' to start development"

  ci:
    desc: "Run all CI checks (type-check, lint, test)"
    cmds:
      - task: type-check
      - task: lint
      - task: test:run

  pre-commit:
    desc: "Run pre-commit checks (lint, type-check, test)"
    cmds:
      - task: lint
      - task: type-check
      - task: test:run
      - echo "✅ Pre-commit checks passed"

  health-check:
    desc: "Check project health and dependencies"
    cmds:
      - task: info
      - npm audit --audit-level=high || echo "⚠️  Security vulnerabilities found (check above)"
      - task: type-check
      - echo "✅ Health check complete"

  # =============================================================================
  # DEVELOPMENT WORKFLOW TASKS
  # =============================================================================

  dev:full:
    desc: "Start development with database and all services"
    deps: [db:generate]
    cmds:
      - task: dev

  dev:fresh:
    desc: "Fresh development start (clean install + setup + dev)"
    cmds:
      - task: install:clean
      - task: db:setup
      - task: dev

  # =============================================================================
  # MAINTENANCE TASKS
  # =============================================================================

  update:deps:
    desc: "Update dependencies (check for outdated packages)"
    cmds:
      - npm outdated
      - echo "Run 'npm update' to update dependencies"

  security:audit:
    desc: "Run security audit on dependencies"
    cmds:
      - npm audit
      - npm audit --audit-level=high

  # =============================================================================
  # DOCKER TASKS (if needed in future)
  # =============================================================================

  docker:build:
    desc: "Build Docker image (if Dockerfile exists)"
    cmds:
      - |
        if [ -f Dockerfile ]; then
          docker build -t onlyrules:latest .
        else
          echo "No Dockerfile found"
        fi
    silent: true

  # =============================================================================
  # QUICK ALIASES
  # =============================================================================

  d:
    desc: "Quick alias for dev"
    cmds:
      - task: dev

  b:
    desc: "Quick alias for build"
    cmds:
      - task: build

  t:
    desc: "Quick alias for test:run"
    cmds:
      - task: test:run

  l:
    desc: "Quick alias for lint"
    cmds:
      - task: lint
