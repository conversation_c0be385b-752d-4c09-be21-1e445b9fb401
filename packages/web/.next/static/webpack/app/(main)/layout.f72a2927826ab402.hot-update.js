"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4d9a19be480c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L3BhY2thZ2VzL3dlYi9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0ZDlhMTliZTQ4MGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/providers/theme-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/theme-provider.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/../../node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/themes */ \"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Custom hook to handle hydration-safe theme detection\nfunction useHydrationSafeTheme() {\n    _s();\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useHydrationSafeTheme.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"useHydrationSafeTheme.useEffect\"], []);\n    // During SSR and initial hydration, always return dark theme\n    // This ensures server and client render the same content initially\n    if (!mounted) {\n        return \"dark\";\n    }\n    // After hydration, return the actual theme\n    const resolvedTheme = theme === \"system\" ? systemTheme || \"dark\" : theme;\n    return resolvedTheme || \"dark\";\n}\n_s(useHydrationSafeTheme, \"wwtKrz9s9rCFQYIOtNuk3Z8/aIo=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\nfunction RadixThemeWrapper(param) {\n    let { children } = param;\n    _s1();\n    const appearance = useHydrationSafeTheme();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__.Theme, {\n        accentColor: \"blue\",\n        grayColor: \"slate\",\n        radius: \"medium\",\n        scaling: \"100%\",\n        appearance: appearance,\n        panelBackground: \"translucent\",\n        hasBackground: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_s1(RadixThemeWrapper, \"Npem3nf4zX0a9kYco+3G6/0TCPE=\", false, function() {\n    return [\n        useHydrationSafeTheme\n    ];\n});\n_c = RadixThemeWrapper;\nfunction ThemeProvider(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        defaultTheme: \"dark\",\n        enableSystem: true,\n        attribute: \"class\",\n        disableTransitionOnChange: false,\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RadixThemeWrapper, {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ThemeProvider;\nvar _c, _c1;\n$RefreshReg$(_c, \"RadixThemeWrapper\");\n$RefreshReg$(_c1, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/providers/theme-provider.tsx\n"));

/***/ })

});