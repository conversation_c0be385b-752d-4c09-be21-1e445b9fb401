"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a4e10ec00e13\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L3BhY2thZ2VzL3dlYi9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhNGUxMGVjMDBlMTNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/providers/theme-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/theme-provider.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/../../node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/themes */ \"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Custom hook to handle hydration-safe theme detection\nfunction useHydrationSafeTheme() {\n    _s();\n    const { resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useHydrationSafeTheme.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"useHydrationSafeTheme.useEffect\"], []);\n    // During SSR and initial hydration, use light theme for consistency\n    // This prevents hydration mismatches with CSS variables\n    if (!mounted) {\n        return \"light\";\n    }\n    // After hydration, return the actual resolved theme\n    return resolvedTheme || \"light\";\n}\n_s(useHydrationSafeTheme, \"7mWKygn7kk6b3+dTNlroOqQjeIs=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\nfunction RadixThemeWrapper(param) {\n    let { children } = param;\n    _s1();\n    const appearance = useHydrationSafeTheme();\n    // DEBUG: Log theme appearance\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"RadixThemeWrapper.useEffect\": ()=>{\n            console.log('DEBUG: Radix theme appearance:', appearance);\n        }\n    }[\"RadixThemeWrapper.useEffect\"], [\n        appearance\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__.Theme, {\n        accentColor: \"blue\",\n        grayColor: \"slate\",\n        radius: \"medium\",\n        scaling: \"100%\",\n        appearance: appearance,\n        panelBackground: \"translucent\",\n        hasBackground: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s1(RadixThemeWrapper, \"hmk7BrhALPfFPIN/V/9yIEeJPis=\", false, function() {\n    return [\n        useHydrationSafeTheme\n    ];\n});\n_c = RadixThemeWrapper;\nfunction ThemeProvider(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        defaultTheme: \"light\",\n        enableSystem: true,\n        attribute: \"class\",\n        disableTransitionOnChange: false,\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RadixThemeWrapper, {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ThemeProvider;\nvar _c, _c1;\n$RefreshReg$(_c, \"RadixThemeWrapper\");\n$RefreshReg$(_c1, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/providers/theme-provider.tsx\n"));

/***/ })

});