"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/better-auth";
exports.ids = ["vendor-chunks/better-auth"];
exports.modules = {

/***/ "(ssr)/../../node_modules/better-auth/dist/client/react/index.mjs":
/*!******************************************************************!*\
  !*** ../../node_modules/better-auth/dist/client/react/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirstLetter: () => (/* binding */ capitalizeFirstLetter),\n/* harmony export */   createAuthClient: () => (/* binding */ createAuthClient),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var _shared_better_auth_A_Crzln_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/better-auth.A_Crzln-.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.A_Crzln-.mjs\");\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/nanostores/listen-keys/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _better_fetch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @better-fetch/fetch */ \"(ssr)/../../node_modules/@better-fetch/fetch/dist/index.js\");\n/* harmony import */ var _shared_better_auth_VTXNLFMT_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/better-auth.VTXNLFMT.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs\");\n/* harmony import */ var _shared_better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../shared/better-auth.8zoxzg-F.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs\");\n/* harmony import */ var _shared_better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/better-auth.DdzSJf-n.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs\");\n/* harmony import */ var _shared_better_auth_Buni1mmI_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shared/better-auth.Buni1mmI.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs\");\n/* harmony import */ var _shared_better_auth_ffWeg50w_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../shared/better-auth.ffWeg50w.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs\");\n\n\n\n\n\n\n\n\n\nfunction useStore(store, options = {}) {\n    let snapshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(store.get());\n    const { keys, deps = [\n        store,\n        keys\n    ] } = options;\n    let subscribe = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useStore.useCallback[subscribe]\": (onChange)=>{\n            const emitChange = {\n                \"useStore.useCallback[subscribe].emitChange\": (value)=>{\n                    if (snapshotRef.current === value) return;\n                    snapshotRef.current = value;\n                    onChange();\n                }\n            }[\"useStore.useCallback[subscribe].emitChange\"];\n            emitChange(store.value);\n            if (keys?.length) {\n                return (0,nanostores__WEBPACK_IMPORTED_MODULE_8__.listenKeys)(store, keys, emitChange);\n            }\n            return store.listen(emitChange);\n        }\n    }[\"useStore.useCallback[subscribe]\"], deps);\n    let get = ()=>snapshotRef.current;\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)(subscribe, get, get);\n}\nfunction getAtomKey(str) {\n    return `use${capitalizeFirstLetter(str)}`;\n}\nfunction capitalizeFirstLetter(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\nfunction createAuthClient(options) {\n    const { pluginPathMethods, pluginsActions, pluginsAtoms, $fetch, $store, atomListeners } = (0,_shared_better_auth_A_Crzln_mjs__WEBPACK_IMPORTED_MODULE_0__.g)(options);\n    let resolvedHooks = {};\n    for (const [key, value] of Object.entries(pluginsAtoms)){\n        resolvedHooks[getAtomKey(key)] = ()=>useStore(value);\n    }\n    const routes = {\n        ...pluginsActions,\n        ...resolvedHooks,\n        $fetch,\n        $store\n    };\n    const proxy = (0,_shared_better_auth_A_Crzln_mjs__WEBPACK_IMPORTED_MODULE_0__.c)(routes, $fetch, pluginPathMethods, pluginsAtoms, atomListeners);\n    return proxy;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/better-auth/dist/client/react/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs":
/*!***************************************************************************!*\
  !*** ../../node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ isProduction),\n/* harmony export */   b: () => (/* binding */ isDevelopment),\n/* harmony export */   e: () => (/* binding */ env),\n/* harmony export */   i: () => (/* binding */ isTest)\n/* harmony export */ });\nconst _envShim = /* @__PURE__ */ Object.create(null);\nconst _getEnv = (useShim)=>globalThis.process?.env || //@ts-expect-error\n    globalThis.Deno?.env.toObject() || //@ts-expect-error\n    globalThis.__env__ || (useShim ? _envShim : globalThis);\nconst env = new Proxy(_envShim, {\n    get (_, prop) {\n        const env2 = _getEnv();\n        return env2[prop] ?? _envShim[prop];\n    },\n    has (_, prop) {\n        const env2 = _getEnv();\n        return prop in env2 || prop in _envShim;\n    },\n    set (_, prop, value) {\n        const env2 = _getEnv(true);\n        env2[prop] = value;\n        return true;\n    },\n    deleteProperty (_, prop) {\n        if (!prop) {\n            return false;\n        }\n        const env2 = _getEnv(true);\n        delete env2[prop];\n        return true;\n    },\n    ownKeys () {\n        const env2 = _getEnv(true);\n        return Object.keys(env2);\n    }\n});\nfunction toBoolean(val) {\n    return val ? val !== \"false\" : false;\n}\nconst nodeENV = typeof process !== \"undefined\" && process.env && \"development\" || \"\";\nconst isProduction = nodeENV === \"production\";\nconst isDevelopment = nodeENV === \"dev\" || nodeENV === \"development\";\nconst isTest = nodeENV === \"test\" || toBoolean(env.TEST);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/better-auth/dist/shared/better-auth.A_Crzln-.mjs":
/*!***************************************************************************!*\
  !*** ../../node_modules/better-auth/dist/shared/better-auth.A_Crzln-.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   c: () => (/* binding */ createDynamicPathProxy),\n/* harmony export */   g: () => (/* binding */ getClientConfig)\n/* harmony export */ });\n/* harmony import */ var _better_fetch_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @better-fetch/fetch */ \"(ssr)/../../node_modules/@better-fetch/fetch/dist/index.js\");\n/* harmony import */ var _better_auth_VTXNLFMT_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./better-auth.VTXNLFMT.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs\");\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/nanostores/atom/index.js\");\n/* harmony import */ var _better_auth_Buni1mmI_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./better-auth.Buni1mmI.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs\");\n/* harmony import */ var _better_auth_ffWeg50w_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./better-auth.ffWeg50w.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs\");\n\n\n\n\n\nconst redirectPlugin = {\n    id: \"redirect\",\n    name: \"Redirect\",\n    hooks: {\n        onSuccess (context) {\n            if (context.data?.url && context.data?.redirect) {\n                if (false) {}\n            }\n        }\n    }\n};\nfunction getSessionAtom($fetch) {\n    const $signal = (0,nanostores__WEBPACK_IMPORTED_MODULE_4__.atom)(false);\n    const session = (0,_better_auth_Buni1mmI_mjs__WEBPACK_IMPORTED_MODULE_2__.u)($signal, \"/get-session\", $fetch, {\n        method: \"GET\"\n    });\n    return {\n        session,\n        $sessionSignal: $signal\n    };\n}\nconst getClientConfig = (options)=>{\n    const isCredentialsSupported = \"credentials\" in Request.prototype;\n    const baseURL = (0,_better_auth_VTXNLFMT_mjs__WEBPACK_IMPORTED_MODULE_1__.a)(options?.baseURL, options?.basePath);\n    const pluginsFetchPlugins = options?.plugins?.flatMap((plugin)=>plugin.fetchPlugins).filter((pl)=>pl !== void 0) || [];\n    const lifeCyclePlugin = {\n        id: \"lifecycle-hooks\",\n        name: \"lifecycle-hooks\",\n        hooks: {\n            onSuccess: options?.fetchOptions?.onSuccess,\n            onError: options?.fetchOptions?.onError,\n            onRequest: options?.fetchOptions?.onRequest,\n            onResponse: options?.fetchOptions?.onResponse\n        }\n    };\n    const { onSuccess, onError, onRequest, onResponse, ...restOfFetchOptions } = options?.fetchOptions || {};\n    const $fetch = (0,_better_fetch_fetch__WEBPACK_IMPORTED_MODULE_0__.createFetch)({\n        baseURL,\n        ...isCredentialsSupported ? {\n            credentials: \"include\"\n        } : {},\n        method: \"GET\",\n        jsonParser (text) {\n            if (!text) {\n                return null;\n            }\n            return (0,_better_auth_ffWeg50w_mjs__WEBPACK_IMPORTED_MODULE_3__.p)(text, {\n                strict: false\n            });\n        },\n        customFetchImpl: async (input, init)=>{\n            try {\n                return await fetch(input, init);\n            } catch (error) {\n                return Response.error();\n            }\n        },\n        ...restOfFetchOptions,\n        plugins: [\n            lifeCyclePlugin,\n            ...restOfFetchOptions.plugins || [],\n            ...options?.disableDefaultFetchPlugins ? [] : [\n                redirectPlugin\n            ],\n            ...pluginsFetchPlugins\n        ]\n    });\n    const { $sessionSignal, session } = getSessionAtom($fetch);\n    const plugins = options?.plugins || [];\n    let pluginsActions = {};\n    let pluginsAtoms = {\n        $sessionSignal,\n        session\n    };\n    let pluginPathMethods = {\n        \"/sign-out\": \"POST\",\n        \"/revoke-sessions\": \"POST\",\n        \"/revoke-other-sessions\": \"POST\",\n        \"/delete-user\": \"POST\"\n    };\n    const atomListeners = [\n        {\n            signal: \"$sessionSignal\",\n            matcher (path) {\n                return path === \"/sign-out\" || path === \"/update-user\" || path.startsWith(\"/sign-in\") || path.startsWith(\"/sign-up\") || path === \"/delete-user\" || path === \"/verify-email\";\n            }\n        }\n    ];\n    for (const plugin of plugins){\n        if (plugin.getAtoms) {\n            Object.assign(pluginsAtoms, plugin.getAtoms?.($fetch));\n        }\n        if (plugin.pathMethods) {\n            Object.assign(pluginPathMethods, plugin.pathMethods);\n        }\n        if (plugin.atomListeners) {\n            atomListeners.push(...plugin.atomListeners);\n        }\n    }\n    const $store = {\n        notify: (signal)=>{\n            pluginsAtoms[signal].set(!pluginsAtoms[signal].get());\n        },\n        listen: (signal, listener)=>{\n            pluginsAtoms[signal].subscribe(listener);\n        },\n        atoms: pluginsAtoms\n    };\n    for (const plugin of plugins){\n        if (plugin.getActions) {\n            Object.assign(pluginsActions, plugin.getActions?.($fetch, $store, options));\n        }\n    }\n    return {\n        pluginsActions,\n        pluginsAtoms,\n        pluginPathMethods,\n        atomListeners,\n        $fetch,\n        $store\n    };\n};\nfunction getMethod(path, knownPathMethods, args) {\n    const method = knownPathMethods[path];\n    const { fetchOptions, query, ...body } = args || {};\n    if (method) {\n        return method;\n    }\n    if (fetchOptions?.method) {\n        return fetchOptions.method;\n    }\n    if (body && Object.keys(body).length > 0) {\n        return \"POST\";\n    }\n    return \"GET\";\n}\nfunction createDynamicPathProxy(routes, client, knownPathMethods, atoms, atomListeners) {\n    function createProxy(path = []) {\n        return new Proxy(function() {}, {\n            get (target, prop) {\n                const fullPath = [\n                    ...path,\n                    prop\n                ];\n                let current = routes;\n                for (const segment of fullPath){\n                    if (current && typeof current === \"object\" && segment in current) {\n                        current = current[segment];\n                    } else {\n                        current = void 0;\n                        break;\n                    }\n                }\n                if (typeof current === \"function\") {\n                    return current;\n                }\n                return createProxy(fullPath);\n            },\n            apply: async (_, __, args)=>{\n                const routePath = \"/\" + path.map((segment)=>segment.replace(/[A-Z]/g, (letter)=>`-${letter.toLowerCase()}`)).join(\"/\");\n                const arg = args[0] || {};\n                const fetchOptions = args[1] || {};\n                const { query, fetchOptions: argFetchOptions, ...body } = arg;\n                const options = {\n                    ...fetchOptions,\n                    ...argFetchOptions\n                };\n                const method = getMethod(routePath, knownPathMethods, arg);\n                return await client(routePath, {\n                    ...options,\n                    body: method === \"GET\" ? void 0 : {\n                        ...body,\n                        ...options?.body || {}\n                    },\n                    query: query || options?.query,\n                    method,\n                    async onSuccess (context) {\n                        await options?.onSuccess?.(context);\n                        const matches = atomListeners?.find((s)=>s.matcher(routePath));\n                        if (!matches) return;\n                        const signal = atoms[matches.signal];\n                        if (!signal) return;\n                        const val = signal.get();\n                        setTimeout(()=>{\n                            signal.set(!val);\n                        }, 10);\n                    }\n                });\n            }\n        });\n    }\n    return createProxy();\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/better-auth/dist/shared/better-auth.A_Crzln-.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs":
/*!***************************************************************************!*\
  !*** ../../node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   u: () => (/* binding */ useAuthQuery)\n/* harmony export */ });\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/nanostores/atom/index.js\");\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/nanostores/lifecycle/index.js\");\n\nconst isServer = \"undefined\" === \"undefined\";\nconst useAuthQuery = (initializedAtom, path, $fetch, options)=>{\n    const value = (0,nanostores__WEBPACK_IMPORTED_MODULE_0__.atom)({\n        data: null,\n        error: null,\n        isPending: true,\n        isRefetching: false,\n        refetch: ()=>{\n            return fn();\n        }\n    });\n    const fn = ()=>{\n        const opts = typeof options === \"function\" ? options({\n            data: value.get().data,\n            error: value.get().error,\n            isPending: value.get().isPending\n        }) : options;\n        return $fetch(path, {\n            ...opts,\n            async onSuccess (context) {\n                value.set({\n                    data: context.data,\n                    error: null,\n                    isPending: false,\n                    isRefetching: false,\n                    refetch: value.value.refetch\n                });\n                await opts?.onSuccess?.(context);\n            },\n            async onError (context) {\n                const { request } = context;\n                const retryAttempts = typeof request.retry === \"number\" ? request.retry : request.retry?.attempts;\n                const retryAttempt = request.retryAttempt || 0;\n                if (retryAttempts && retryAttempt < retryAttempts) return;\n                value.set({\n                    error: context.error,\n                    data: null,\n                    isPending: false,\n                    isRefetching: false,\n                    refetch: value.value.refetch\n                });\n                await opts?.onError?.(context);\n            },\n            async onRequest (context) {\n                const currentValue = value.get();\n                value.set({\n                    isPending: currentValue.data === null,\n                    data: currentValue.data,\n                    error: null,\n                    isRefetching: true,\n                    refetch: value.value.refetch\n                });\n                await opts?.onRequest?.(context);\n            }\n        });\n    };\n    initializedAtom = Array.isArray(initializedAtom) ? initializedAtom : [\n        initializedAtom\n    ];\n    let isMounted = false;\n    for (const initAtom of initializedAtom){\n        initAtom.subscribe(()=>{\n            if (isServer) {\n                return;\n            }\n            if (isMounted) {\n                fn();\n            } else {\n                (0,nanostores__WEBPACK_IMPORTED_MODULE_1__.onMount)(value, ()=>{\n                    setTimeout(()=>{\n                        fn();\n                    }, 0);\n                    isMounted = true;\n                    return ()=>{\n                        value.off();\n                        initAtom.off();\n                    };\n                });\n            }\n        });\n    }\n    return value;\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs":
/*!***************************************************************************!*\
  !*** ../../node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   B: () => (/* binding */ BetterAuthError),\n/* harmony export */   M: () => (/* binding */ MissingDependencyError)\n/* harmony export */ });\nclass BetterAuthError extends Error {\n    constructor(message, cause){\n        super(message);\n        this.name = \"BetterAuthError\";\n        this.message = message;\n        this.cause = cause;\n        this.stack = \"\";\n    }\n}\nclass MissingDependencyError extends BetterAuthError {\n    constructor(pkgName){\n        super(`The package \"${pkgName}\" is required. Make sure it is installed.`, pkgName);\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2JldHRlci1hdXRoL2Rpc3Qvc2hhcmVkL2JldHRlci1hdXRoLkRkelNKZi1uLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLE1BQU1BLHdCQUF3QkM7SUFDNUIsWUFBWUMsT0FBTyxFQUFFQyxLQUFLLENBQUU7UUFDMUIsS0FBSyxDQUFDRDtRQUNOLElBQUksQ0FBQ0UsSUFBSSxHQUFHO1FBQ1osSUFBSSxDQUFDRixPQUFPLEdBQUdBO1FBQ2YsSUFBSSxDQUFDQyxLQUFLLEdBQUdBO1FBQ2IsSUFBSSxDQUFDRSxLQUFLLEdBQUc7SUFDZjtBQUNGO0FBQ0EsTUFBTUMsK0JBQStCTjtJQUNuQyxZQUFZTyxPQUFPLENBQUU7UUFDbkIsS0FBSyxDQUNILENBQUMsYUFBYSxFQUFFQSxRQUFRLHlDQUF5QyxDQUFDLEVBQ2xFQTtJQUVKO0FBQ0Y7QUFFNkQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9iZXR0ZXItYXV0aC9kaXN0L3NoYXJlZC9iZXR0ZXItYXV0aC5EZHpTSmYtbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY2xhc3MgQmV0dGVyQXV0aEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb25zdHJ1Y3RvcihtZXNzYWdlLCBjYXVzZSkge1xuICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgIHRoaXMubmFtZSA9IFwiQmV0dGVyQXV0aEVycm9yXCI7XG4gICAgdGhpcy5tZXNzYWdlID0gbWVzc2FnZTtcbiAgICB0aGlzLmNhdXNlID0gY2F1c2U7XG4gICAgdGhpcy5zdGFjayA9IFwiXCI7XG4gIH1cbn1cbmNsYXNzIE1pc3NpbmdEZXBlbmRlbmN5RXJyb3IgZXh0ZW5kcyBCZXR0ZXJBdXRoRXJyb3Ige1xuICBjb25zdHJ1Y3Rvcihwa2dOYW1lKSB7XG4gICAgc3VwZXIoXG4gICAgICBgVGhlIHBhY2thZ2UgXCIke3BrZ05hbWV9XCIgaXMgcmVxdWlyZWQuIE1ha2Ugc3VyZSBpdCBpcyBpbnN0YWxsZWQuYCxcbiAgICAgIHBrZ05hbWVcbiAgICApO1xuICB9XG59XG5cbmV4cG9ydCB7IEJldHRlckF1dGhFcnJvciBhcyBCLCBNaXNzaW5nRGVwZW5kZW5jeUVycm9yIGFzIE0gfTtcbiJdLCJuYW1lcyI6WyJCZXR0ZXJBdXRoRXJyb3IiLCJFcnJvciIsIm1lc3NhZ2UiLCJjYXVzZSIsIm5hbWUiLCJzdGFjayIsIk1pc3NpbmdEZXBlbmRlbmN5RXJyb3IiLCJwa2dOYW1lIiwiQiIsIk0iXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs":
/*!***************************************************************************!*\
  !*** ../../node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ getBaseURL),\n/* harmony export */   b: () => (/* binding */ getHost),\n/* harmony export */   c: () => (/* binding */ getProtocol),\n/* harmony export */   g: () => (/* binding */ getOrigin)\n/* harmony export */ });\n/* harmony import */ var _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./better-auth.8zoxzg-F.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs\");\n/* harmony import */ var _better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./better-auth.DdzSJf-n.mjs */ \"(ssr)/../../node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs\");\n\n\nfunction checkHasPath(url) {\n    try {\n        const parsedUrl = new URL(url);\n        return parsedUrl.pathname !== \"/\";\n    } catch (error) {\n        throw new _better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_1__.B(`Invalid base URL: ${url}. Please provide a valid base URL.`);\n    }\n}\nfunction withPath(url, path = \"/api/auth\") {\n    const hasPath = checkHasPath(url);\n    if (hasPath) {\n        return url;\n    }\n    path = path.startsWith(\"/\") ? path : `/${path}`;\n    return `${url.replace(/\\/+$/, \"\")}${path}`;\n}\nfunction getBaseURL(url, path, request) {\n    if (url) {\n        return withPath(url, path);\n    }\n    const fromEnv = _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.NEXT_PUBLIC_BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.PUBLIC_BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.NUXT_PUBLIC_BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.NUXT_PUBLIC_AUTH_URL || (_better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.BASE_URL !== \"/\" ? _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.BASE_URL : void 0);\n    if (fromEnv) {\n        return withPath(fromEnv, path);\n    }\n    const fromRequest = request?.headers.get(\"x-forwarded-host\");\n    const fromRequestProto = request?.headers.get(\"x-forwarded-proto\");\n    if (fromRequest && fromRequestProto) {\n        return withPath(`${fromRequestProto}://${fromRequest}`, path);\n    }\n    if (request) {\n        const url2 = getOrigin(request.url);\n        if (!url2) {\n            throw new _better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_1__.B(\"Could not get origin from request. Please provide a valid base URL.\");\n        }\n        return withPath(url2, path);\n    }\n    if (false) {}\n    return void 0;\n}\nfunction getOrigin(url) {\n    try {\n        const parsedUrl = new URL(url);\n        return parsedUrl.origin;\n    } catch (error) {\n        return null;\n    }\n}\nfunction getProtocol(url) {\n    try {\n        const parsedUrl = new URL(url);\n        return parsedUrl.protocol;\n    } catch (error) {\n        return null;\n    }\n}\nfunction getHost(url) {\n    try {\n        const parsedUrl = new URL(url);\n        return parsedUrl.host;\n    } catch (error) {\n        return url;\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2JldHRlci1hdXRoL2Rpc3Qvc2hhcmVkL2JldHRlci1hdXRoLlZUWE5MRk1ULm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBc0Q7QUFDWTtBQUVsRSxTQUFTSSxhQUFhQyxHQUFHO0lBQ3ZCLElBQUk7UUFDRixNQUFNQyxZQUFZLElBQUlDLElBQUlGO1FBQzFCLE9BQU9DLFVBQVVFLFFBQVEsS0FBSztJQUNoQyxFQUFFLE9BQU9DLE9BQU87UUFDZCxNQUFNLElBQUlOLHdEQUFlQSxDQUN2QixDQUFDLGtCQUFrQixFQUFFRSxJQUFJLGtDQUFrQyxDQUFDO0lBRWhFO0FBQ0Y7QUFDQSxTQUFTSyxTQUFTTCxHQUFHLEVBQUVNLE9BQU8sV0FBVztJQUN2QyxNQUFNQyxVQUFVUixhQUFhQztJQUM3QixJQUFJTyxTQUFTO1FBQ1gsT0FBT1A7SUFDVDtJQUNBTSxPQUFPQSxLQUFLRSxVQUFVLENBQUMsT0FBT0YsT0FBTyxDQUFDLENBQUMsRUFBRUEsTUFBTTtJQUMvQyxPQUFPLEdBQUdOLElBQUlTLE9BQU8sQ0FBQyxRQUFRLE1BQU1ILE1BQU07QUFDNUM7QUFDQSxTQUFTSSxXQUFXVixHQUFHLEVBQUVNLElBQUksRUFBRUssT0FBTztJQUNwQyxJQUFJWCxLQUFLO1FBQ1AsT0FBT0ssU0FBU0wsS0FBS007SUFDdkI7SUFDQSxNQUFNTSxVQUFVaEIsd0RBQUdBLENBQUNpQixlQUFlLElBQUlqQix3REFBR0EsQ0FBQ2tCLDJCQUEyQixJQUFJbEIsd0RBQUdBLENBQUNtQixzQkFBc0IsSUFBSW5CLHdEQUFHQSxDQUFDb0IsMkJBQTJCLElBQUlwQix3REFBR0EsQ0FBQ3FCLG9CQUFvQixJQUFLckIsQ0FBQUEsd0RBQUdBLENBQUNzQixRQUFRLEtBQUssTUFBTXRCLHdEQUFHQSxDQUFDc0IsUUFBUSxHQUFHLEtBQUs7SUFDbk4sSUFBSU4sU0FBUztRQUNYLE9BQU9QLFNBQVNPLFNBQVNOO0lBQzNCO0lBQ0EsTUFBTWEsY0FBY1IsU0FBU1MsUUFBUUMsSUFBSTtJQUN6QyxNQUFNQyxtQkFBbUJYLFNBQVNTLFFBQVFDLElBQUk7SUFDOUMsSUFBSUYsZUFBZUcsa0JBQWtCO1FBQ25DLE9BQU9qQixTQUFTLEdBQUdpQixpQkFBaUIsR0FBRyxFQUFFSCxhQUFhLEVBQUViO0lBQzFEO0lBQ0EsSUFBSUssU0FBUztRQUNYLE1BQU1ZLE9BQU9DLFVBQVViLFFBQVFYLEdBQUc7UUFDbEMsSUFBSSxDQUFDdUIsTUFBTTtZQUNULE1BQU0sSUFBSXpCLHdEQUFlQSxDQUN2QjtRQUVKO1FBQ0EsT0FBT08sU0FBU2tCLE1BQU1qQjtJQUN4QjtJQUNBLElBQUksS0FBZ0QsRUFBRSxFQUVyRDtJQUNELE9BQU8sS0FBSztBQUNkO0FBQ0EsU0FBU2tCLFVBQVV4QixHQUFHO0lBQ3BCLElBQUk7UUFDRixNQUFNQyxZQUFZLElBQUlDLElBQUlGO1FBQzFCLE9BQU9DLFVBQVUwQixNQUFNO0lBQ3pCLEVBQUUsT0FBT3ZCLE9BQU87UUFDZCxPQUFPO0lBQ1Q7QUFDRjtBQUNBLFNBQVN3QixZQUFZNUIsR0FBRztJQUN0QixJQUFJO1FBQ0YsTUFBTUMsWUFBWSxJQUFJQyxJQUFJRjtRQUMxQixPQUFPQyxVQUFVNEIsUUFBUTtJQUMzQixFQUFFLE9BQU96QixPQUFPO1FBQ2QsT0FBTztJQUNUO0FBQ0Y7QUFDQSxTQUFTMEIsUUFBUTlCLEdBQUc7SUFDbEIsSUFBSTtRQUNGLE1BQU1DLFlBQVksSUFBSUMsSUFBSUY7UUFDMUIsT0FBT0MsVUFBVThCLElBQUk7SUFDdkIsRUFBRSxPQUFPM0IsT0FBTztRQUNkLE9BQU9KO0lBQ1Q7QUFDRjtBQUUyRSIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2JldHRlci1hdXRoL2Rpc3Qvc2hhcmVkL2JldHRlci1hdXRoLlZUWE5MRk1ULm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBlIGFzIGVudiB9IGZyb20gJy4vYmV0dGVyLWF1dGguOHpveHpnLUYubWpzJztcbmltcG9ydCB7IEIgYXMgQmV0dGVyQXV0aEVycm9yIH0gZnJvbSAnLi9iZXR0ZXItYXV0aC5EZHpTSmYtbi5tanMnO1xuXG5mdW5jdGlvbiBjaGVja0hhc1BhdGgodXJsKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcGFyc2VkVXJsID0gbmV3IFVSTCh1cmwpO1xuICAgIHJldHVybiBwYXJzZWRVcmwucGF0aG5hbWUgIT09IFwiL1wiO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHRocm93IG5ldyBCZXR0ZXJBdXRoRXJyb3IoXG4gICAgICBgSW52YWxpZCBiYXNlIFVSTDogJHt1cmx9LiBQbGVhc2UgcHJvdmlkZSBhIHZhbGlkIGJhc2UgVVJMLmBcbiAgICApO1xuICB9XG59XG5mdW5jdGlvbiB3aXRoUGF0aCh1cmwsIHBhdGggPSBcIi9hcGkvYXV0aFwiKSB7XG4gIGNvbnN0IGhhc1BhdGggPSBjaGVja0hhc1BhdGgodXJsKTtcbiAgaWYgKGhhc1BhdGgpIHtcbiAgICByZXR1cm4gdXJsO1xuICB9XG4gIHBhdGggPSBwYXRoLnN0YXJ0c1dpdGgoXCIvXCIpID8gcGF0aCA6IGAvJHtwYXRofWA7XG4gIHJldHVybiBgJHt1cmwucmVwbGFjZSgvXFwvKyQvLCBcIlwiKX0ke3BhdGh9YDtcbn1cbmZ1bmN0aW9uIGdldEJhc2VVUkwodXJsLCBwYXRoLCByZXF1ZXN0KSB7XG4gIGlmICh1cmwpIHtcbiAgICByZXR1cm4gd2l0aFBhdGgodXJsLCBwYXRoKTtcbiAgfVxuICBjb25zdCBmcm9tRW52ID0gZW52LkJFVFRFUl9BVVRIX1VSTCB8fCBlbnYuTkVYVF9QVUJMSUNfQkVUVEVSX0FVVEhfVVJMIHx8IGVudi5QVUJMSUNfQkVUVEVSX0FVVEhfVVJMIHx8IGVudi5OVVhUX1BVQkxJQ19CRVRURVJfQVVUSF9VUkwgfHwgZW52Lk5VWFRfUFVCTElDX0FVVEhfVVJMIHx8IChlbnYuQkFTRV9VUkwgIT09IFwiL1wiID8gZW52LkJBU0VfVVJMIDogdm9pZCAwKTtcbiAgaWYgKGZyb21FbnYpIHtcbiAgICByZXR1cm4gd2l0aFBhdGgoZnJvbUVudiwgcGF0aCk7XG4gIH1cbiAgY29uc3QgZnJvbVJlcXVlc3QgPSByZXF1ZXN0Py5oZWFkZXJzLmdldChcIngtZm9yd2FyZGVkLWhvc3RcIik7XG4gIGNvbnN0IGZyb21SZXF1ZXN0UHJvdG8gPSByZXF1ZXN0Py5oZWFkZXJzLmdldChcIngtZm9yd2FyZGVkLXByb3RvXCIpO1xuICBpZiAoZnJvbVJlcXVlc3QgJiYgZnJvbVJlcXVlc3RQcm90bykge1xuICAgIHJldHVybiB3aXRoUGF0aChgJHtmcm9tUmVxdWVzdFByb3RvfTovLyR7ZnJvbVJlcXVlc3R9YCwgcGF0aCk7XG4gIH1cbiAgaWYgKHJlcXVlc3QpIHtcbiAgICBjb25zdCB1cmwyID0gZ2V0T3JpZ2luKHJlcXVlc3QudXJsKTtcbiAgICBpZiAoIXVybDIpIHtcbiAgICAgIHRocm93IG5ldyBCZXR0ZXJBdXRoRXJyb3IoXG4gICAgICAgIFwiQ291bGQgbm90IGdldCBvcmlnaW4gZnJvbSByZXF1ZXN0LiBQbGVhc2UgcHJvdmlkZSBhIHZhbGlkIGJhc2UgVVJMLlwiXG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4gd2l0aFBhdGgodXJsMiwgcGF0aCk7XG4gIH1cbiAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIgJiYgd2luZG93LmxvY2F0aW9uKSB7XG4gICAgcmV0dXJuIHdpdGhQYXRoKHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4sIHBhdGgpO1xuICB9XG4gIHJldHVybiB2b2lkIDA7XG59XG5mdW5jdGlvbiBnZXRPcmlnaW4odXJsKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcGFyc2VkVXJsID0gbmV3IFVSTCh1cmwpO1xuICAgIHJldHVybiBwYXJzZWRVcmwub3JpZ2luO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5mdW5jdGlvbiBnZXRQcm90b2NvbCh1cmwpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBwYXJzZWRVcmwgPSBuZXcgVVJMKHVybCk7XG4gICAgcmV0dXJuIHBhcnNlZFVybC5wcm90b2NvbDtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxufVxuZnVuY3Rpb24gZ2V0SG9zdCh1cmwpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBwYXJzZWRVcmwgPSBuZXcgVVJMKHVybCk7XG4gICAgcmV0dXJuIHBhcnNlZFVybC5ob3N0O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHJldHVybiB1cmw7XG4gIH1cbn1cblxuZXhwb3J0IHsgZ2V0QmFzZVVSTCBhcyBhLCBnZXRIb3N0IGFzIGIsIGdldFByb3RvY29sIGFzIGMsIGdldE9yaWdpbiBhcyBnIH07XG4iXSwibmFtZXMiOlsiZSIsImVudiIsIkIiLCJCZXR0ZXJBdXRoRXJyb3IiLCJjaGVja0hhc1BhdGgiLCJ1cmwiLCJwYXJzZWRVcmwiLCJVUkwiLCJwYXRobmFtZSIsImVycm9yIiwid2l0aFBhdGgiLCJwYXRoIiwiaGFzUGF0aCIsInN0YXJ0c1dpdGgiLCJyZXBsYWNlIiwiZ2V0QmFzZVVSTCIsInJlcXVlc3QiLCJmcm9tRW52IiwiQkVUVEVSX0FVVEhfVVJMIiwiTkVYVF9QVUJMSUNfQkVUVEVSX0FVVEhfVVJMIiwiUFVCTElDX0JFVFRFUl9BVVRIX1VSTCIsIk5VWFRfUFVCTElDX0JFVFRFUl9BVVRIX1VSTCIsIk5VWFRfUFVCTElDX0FVVEhfVVJMIiwiQkFTRV9VUkwiLCJmcm9tUmVxdWVzdCIsImhlYWRlcnMiLCJnZXQiLCJmcm9tUmVxdWVzdFByb3RvIiwidXJsMiIsImdldE9yaWdpbiIsIndpbmRvdyIsImxvY2F0aW9uIiwib3JpZ2luIiwiZ2V0UHJvdG9jb2wiLCJwcm90b2NvbCIsImdldEhvc3QiLCJob3N0IiwiYSIsImIiLCJjIiwiZyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs":
/*!***************************************************************************!*\
  !*** ../../node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   p: () => (/* binding */ parseJSON)\n/* harmony export */ });\nconst PROTO_POLLUTION_PATTERNS = {\n    proto: /\"(?:_|\\\\u0{2}5[Ff]){2}(?:p|\\\\u0{2}70)(?:r|\\\\u0{2}72)(?:o|\\\\u0{2}6[Ff])(?:t|\\\\u0{2}74)(?:o|\\\\u0{2}6[Ff])(?:_|\\\\u0{2}5[Ff]){2}\"\\s*:/,\n    constructor: /\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/,\n    protoShort: /\"__proto__\"\\s*:/,\n    constructorShort: /\"constructor\"\\s*:/\n};\nconst JSON_SIGNATURE = /^\\s*[\"[{]|^\\s*-?\\d{1,16}(\\.\\d{1,17})?([Ee][+-]?\\d+)?\\s*$/;\nconst SPECIAL_VALUES = {\n    true: true,\n    false: false,\n    null: null,\n    undefined: void 0,\n    nan: Number.NaN,\n    infinity: Number.POSITIVE_INFINITY,\n    \"-infinity\": Number.NEGATIVE_INFINITY\n};\nconst ISO_DATE_REGEX = /^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{1,7}))?(?:Z|([+-])(\\d{2}):(\\d{2}))$/;\nfunction isValidDate(date) {\n    return date instanceof Date && !isNaN(date.getTime());\n}\nfunction parseISODate(value) {\n    const match = ISO_DATE_REGEX.exec(value);\n    if (!match) return null;\n    const [, year, month, day, hour, minute, second, ms, offsetSign, offsetHour, offsetMinute] = match;\n    let date = new Date(Date.UTC(parseInt(year, 10), parseInt(month, 10) - 1, parseInt(day, 10), parseInt(hour, 10), parseInt(minute, 10), parseInt(second, 10), ms ? parseInt(ms.padEnd(3, \"0\"), 10) : 0));\n    if (offsetSign) {\n        const offset = (parseInt(offsetHour, 10) * 60 + parseInt(offsetMinute, 10)) * (offsetSign === \"+\" ? -1 : 1);\n        date.setUTCMinutes(date.getUTCMinutes() + offset);\n    }\n    return isValidDate(date) ? date : null;\n}\nfunction betterJSONParse(value, options = {}) {\n    const { strict = false, warnings = false, reviver, parseDates = true } = options;\n    if (typeof value !== \"string\") {\n        return value;\n    }\n    const trimmed = value.trim();\n    if (trimmed[0] === '\"' && trimmed.endsWith('\"') && !trimmed.slice(1, -1).includes('\"')) {\n        return trimmed.slice(1, -1);\n    }\n    const lowerValue = trimmed.toLowerCase();\n    if (lowerValue.length <= 9 && lowerValue in SPECIAL_VALUES) {\n        return SPECIAL_VALUES[lowerValue];\n    }\n    if (!JSON_SIGNATURE.test(trimmed)) {\n        if (strict) {\n            throw new SyntaxError(\"[better-json] Invalid JSON\");\n        }\n        return value;\n    }\n    const hasProtoPattern = Object.entries(PROTO_POLLUTION_PATTERNS).some(([key, pattern])=>{\n        const matches = pattern.test(trimmed);\n        if (matches && warnings) {\n            console.warn(`[better-json] Detected potential prototype pollution attempt using ${key} pattern`);\n        }\n        return matches;\n    });\n    if (hasProtoPattern && strict) {\n        throw new Error(\"[better-json] Potential prototype pollution attempt detected\");\n    }\n    try {\n        const secureReviver = (key, value2)=>{\n            if (key === \"__proto__\" || key === \"constructor\" && value2 && typeof value2 === \"object\" && \"prototype\" in value2) {\n                if (warnings) {\n                    console.warn(`[better-json] Dropping \"${key}\" key to prevent prototype pollution`);\n                }\n                return void 0;\n            }\n            if (parseDates && typeof value2 === \"string\") {\n                const date = parseISODate(value2);\n                if (date) {\n                    return date;\n                }\n            }\n            return reviver ? reviver(key, value2) : value2;\n        };\n        return JSON.parse(trimmed, secureReviver);\n    } catch (error) {\n        if (strict) {\n            throw error;\n        }\n        return value;\n    }\n}\nfunction parseJSON(value, options = {\n    strict: true\n}) {\n    return betterJSONParse(value, options);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs\n");

/***/ })

};
;