"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes";
import { Theme } from "@radix-ui/themes";
import { useTheme } from "next-themes";

// Custom hook to handle hydration-safe theme detection
function useHydrationSafeTheme() {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  // During SSR and initial hydration, use light theme for consistency
  // This prevents hydration mismatches with CSS variables
  if (!mounted) {
    return "light";
  }

  // After hydration, return the actual resolved theme
  return (resolvedTheme as "light" | "dark") || "light";
}

function RadixThemeWrapper({ children }: { children: React.ReactNode }) {
  const appearance = useHydrationSafeTheme();

  return (
    <Theme
      accentColor="blue"
      grayColor="slate"
      radius="medium"
      scaling="100%"
      appearance={appearance}
      panelBackground="translucent"
      hasBackground={true}
    >
      {children}
    </Theme>
  );
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      defaultTheme="light"
      enableSystem={true}
      attribute="class"
      disableTransitionOnChange={false}
      {...props}
    >
      <RadixThemeWrapper>{children}</RadixThemeWrapper>
    </NextThemesProvider>
  );
}